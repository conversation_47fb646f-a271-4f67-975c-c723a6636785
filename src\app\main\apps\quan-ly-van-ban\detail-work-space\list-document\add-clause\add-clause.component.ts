import { Component, Input, OnInit } from "@angular/core";
import { Form<PERSON>uilder, FormGroup, Validators } from "@angular/forms";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import { FormType } from "app/models/FormType";
import { ToastrService } from "ngx-toastr";
import { ListDocumentService } from "../list-document.service";

@Component({
  selector: "app-add-clause",
  templateUrl: "./add-clause.component.html",
  styleUrls: ["./add-clause.component.scss"],
})
export class AddClauseComponent implements OnInit {
  @Input("modal") public modal: NgbActiveModal;
  @Input("title") public title: string;
  @Input("type") public type: number;
  @Input("row") public row: any;
  @Input("existingClauses") public existingClauses: any[] = [];

  public formClause: FormGroup;
  public submitted = false;
  public isDuplicateClauseId = false;
  public duplicateWarningMessage = "";
  constructor(
    private fb: FormBuilder,
    private listDocument: ListDocumentService,
    private toast: ToastrService
  ) { }

  ngOnInit(): void {
    this.formClause = this.fb.group({
      clause_id: [null, [Validators.required, Validators.pattern("^[0-9]*$")]],
      title: ["", Validators.required],
      position: ["", Validators.required],
      raw_content: ["", Validators.required],
      show_content: [""],
      doc_id: [""],
      is_raw: [""],
    });

    // Lắng nghe thay đổi của clause_id để kiểm tra trùng lặp
    this.formClause.get('clause_id')?.valueChanges.subscribe(value => {
      this.checkDuplicateClauseId(value);
    });

    if (this.type == FormType.Update) {
      console.log(this.row)
      this.formClause.patchValue({
        ...this.row,
        clause_id: this.row.clause_id.split("_")[1],
      });
    }
  }
  get f() {
    return this.formClause.controls;
  }

  checkDuplicateClauseId(clauseId: any) {
    if (!clauseId) {
      this.isDuplicateClauseId = false;
      this.duplicateWarningMessage = "";
      return;
    }

    const clauseIdToCheck = `dieu_${clauseId}`;

    // Nếu đang update, loại trừ chính điều khoản đang được update
    let clausesToCheck = this.existingClauses;
    if (this.type === FormType.Update && this.row) {
      clausesToCheck = this.existingClauses.filter(clause =>
        clause.id !== this.row.id
      );
    }

    const isDuplicate = clausesToCheck.some(clause =>
      clause.clause_id === clauseIdToCheck
    );

    if (isDuplicate) {
      this.isDuplicateClauseId = true;
      this.duplicateWarningMessage = `Điều ${clauseId} đã tồn tại trong văn bản.`;
    } else {
      this.isDuplicateClauseId = false;
      this.duplicateWarningMessage = "";
    }
  }

  onSubmit() {
    this.submitted = true;
    if (this.formClause.invalid || this.isDuplicateClauseId) {
      return;
    }
    const clauseId = this.formClause.value.clause_id;

    const body = {
      clause_id: `dieu_${clauseId}`,
      title: this.formClause.value.title,
      position: this.formClause.value.position,
      raw_content: this.formClause.value.raw_content,
      show_content: this.formClause.value.show_content,
      doc_id: this.formClause.value.doc_id,
      is_raw: this.formClause.value.is_raw,
    };
    if (this.type == FormType.Create) {
      this.listDocument.addDieuKhoan(this.row.id, body).subscribe((res) => {
        if (res.status === "success") {
          this.toast.success(res.message, "Thành công", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
          this.listDocument.refreshClause.next(true);
          this.modal.close();
        } else {
          this.toast.error(res.message, "Thất bại", {
            closeButton: true,
            positionClass: "toast-top-right",
            toastClass: "toast ngx-toastr",
          });
        }
      });
    } else {
      this.listDocument
        .updateClause(this.formClause.value, this.row.id)
        .subscribe((res) => {
          if (res.status === "success") {
            this.toast.success(res.message, "Thành công", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
            this.listDocument.refreshClause.next(true);
            this.modal.close();
          } else {
            this.toast.error(res.message, "Thất bại", {
              closeButton: true,
              positionClass: "toast-top-right",
              toastClass: "toast ngx-toastr",
            });
          }
        });
    }
  }
  ngOndestroy() {
    this.listDocument.refreshClause.next(false);
  }
}
